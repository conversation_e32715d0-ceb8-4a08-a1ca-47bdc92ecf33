import 'package:dio/dio.dart';
import 'package:family_management/data/datasources/remote_datasources/base_remotedatasource.dart';

class EventRemotedatasource extends BaseRemotedatasource {
  EventRemotedatasource({required super.dio});

  Future<Response> storeOrUpdate(
      {int? id,
      required String name,
      required String description,
      required String scheduledAt,
      required int familyId,
      required String type,
      required String notifyTime,
      required String occasionId,
      String? location,
      int? recurringInterval,
      String? recurringPattern}) async {
    final data = {
      'name': name,
      'description': description,
      'scheduled_at': scheduledAt,
      'family_id': familyId,
      'type': type,
      'notify_time': notifyTime,
      'occasion_id': occasionId,
      if (type == 'recurring') ...{
        'recurring_pattern': recurringPattern,
        'recurring_interval': recurringInterval,
      },
    };
    if (location != null && location.isNotEmpty) {
      data['location'] = location;
    }

    return await post(
        endpoint: id != null ? 'events/$id' : 'events', data: data);
  }

  Future<Response> update(
      {required int id,
      required String? name,
      required String? description,
      required String? scheduledAt,
      required String? type,
      required String? notifyTime,
      required String? occasionId,
      String? recurringInterval}) async {
    final data = {};
    if (name != null) {
      data['name'] = name;
    }
    if (description != null) {
      data['description'] = description;
    }
    if (scheduledAt != null) {
      data['scheduled_at'] = scheduledAt;
    }
    if (type != null) {
      data['type'] = type;
    }
    if (notifyTime != null) {
      data['notify_time'] = notifyTime;
    }
    if (occasionId != null) {
      data['occasion_id'] = occasionId;
    }
    if (recurringInterval != null) {
      data['recurring_interval'] = recurringInterval;
    }
    return await put(endpoint: 'events/$id', data: data);
  }

  Future<Response> getEvents(int familyId,
      {String? date, String groupBy = 'day', String? search}) async {
    String url = 'events?familyId=$familyId&groupBy=$groupBy';
    if (date != null) {
      url += '&date=$date';
    }
    if (search != null && search.isNotEmpty) {
      url += '&search=$search';
    }
    return await get(endpoint: url);
  }

  Future<Response> deleteEvent(int id) async {
    return await delete(endpoint: 'events/$id');
  }

  Future<Response> getOccasions(int familyId) async {
    return await get(endpoint: 'occasions?family_id=$familyId');
  }
}
