import 'package:family_management/data/datasources/remote_datasources/family_remotedatasource.dart';
import 'package:family_management/domain/models/family.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/failure.dart';
import 'package:fpdart/fpdart.dart';

class FamilyRepository {
  final FamilyRemotedatasource remotedatasource;
  FamilyRepository({required this.remotedatasource});

  Future<Either<Failure, Family>> register(String name) async {
    try {
      final response = await remotedatasource.register(name);
      final family = Family.formJson(response.data['data']);
      dioClient.options.queryParameters = {'family_id': family.id};
      return Right(family);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> sendInvites(
      {required List<String> emails, required int familyId}) async {
    try {
      await remotedatasource.sendInvites(emails: emails, familyId: familyId);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }
}
