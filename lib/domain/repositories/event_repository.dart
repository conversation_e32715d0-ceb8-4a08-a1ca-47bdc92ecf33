import 'package:family_management/data/datasources/remote_datasources/event_remotedatasource.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/models/occasion.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';

class EventRepository {
  final EventRemotedatasource remotedatasource;

  EventRepository({required this.remotedatasource});

  Future<Either<Failure, Event>> storeOrUpdate(
      {int? id,
      required String name,
      required String description,
      required String scheduledAt,
      required int familyId,
      required String type,
      required String occasionId,
      required String notifyTime,
      String? location,
      int? recurringInterval,
      String? recurringPattern}) async {
    try {
      final response = await remotedatasource.storeOrUpdate(
        id: id,
        name: name,
        description: description,
        scheduledAt: scheduledAt,
        familyId: familyId,
        type: type,
        occasionId: occasionId,
        location: location,
        notifyTime: notifyTime,
        recurringInterval: recurringInterval,
        recurringPattern: recurringPattern,
      );
      final event = Event.fromJson(response.data['data']);
      return Right(event);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, Event>> updateEvent(
      {required int id,
      required String? name,
      required String? description,
      required String? scheduledAt,
      required String? type,
      required String? occasionId,
      required String? notifyTime,
      String? recurringInterval}) async {
    try {
      final response = await remotedatasource.update(
          id: id,
          name: name,
          description: description,
          scheduledAt: scheduledAt,
          type: type,
          occasionId: occasionId,
          notifyTime: notifyTime);
      final event = Event.fromJson(response.data['data']);
      return Right(event);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<Event>>> getEvents(int familyId,
      {String? date, String groupBy = 'day', String? search}) async {
    try {
      final response = await remotedatasource.getEvents(familyId,
          date: date, groupBy: groupBy, search: search);
      List<Event> events = [];
      for (final item in response.data['data']) {
        events.add(Event.fromJson(item));
      }
      return Right(events);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, bool>> deleteEvent(int id) async {
    try {
      await remotedatasource.deleteEvent(id);
      return const Right(true);
    } catch (e) {
      return Left(Failure.handleException(e));
    }
  }

  Future<Either<Failure, List<Occasion>>> getOccasions(int familyId) async {
    try {
      final response = await remotedatasource.getOccasions(familyId);
      List<Occasion> occasions = <Occasion>[
        ...response.data['data'].map((e) => Occasion.fromJson(e)).toList()
      ];
      return Right(occasions);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }
}
