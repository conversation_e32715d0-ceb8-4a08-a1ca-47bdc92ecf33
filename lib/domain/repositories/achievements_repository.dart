import 'package:family_management/data/datasources/remote_datasources/achievements_datasource.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/repositories/base_repository.dart';
import 'package:family_management/utils/failure.dart';
import 'package:family_management/utils/utils.dart';
import 'package:fpdart/fpdart.dart';
import 'package:family_management/domain/models/task.dart' as task;

class AchievementsRepository extends BaseRepository {
  final AchievementsDatasource remoteDatasource;
  AchievementsRepository(
      {required super.baseRemotedatasource, required this.remoteDatasource});

  Future<Either<Failure, Map>> getAchievements(int familyId) async {
    try {
      final response = await remoteDatasource.achievements(familyId: familyId);
      final Map data = {};
      List<task.Task> tasks = <task.Task>[
        ...response.data['data']['tasks']
            ?.map((e) => task.Task.formJson(e))
            .toList()
      ];
      List<Event> events = <Event>[
        ...response.data['data']['events']?.map((e) => Event.fromJson(e))
      ].toList();
      List<Event> occasions = <Event>[
        ...response.data['data']['by_occasion']
            ?.map((e) => Event.fromJson(e))
            .toList()
      ];
      data['tasks'] = tasks;
      data['events'] = events;
      data['occasions'] = occasions;
      return Right(data);
    } catch (e) {
      Utils.log(e);
      return Left(Failure.handleException(e));
    }
  }
}
