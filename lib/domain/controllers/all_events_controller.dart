import 'package:family_management/app_service.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/event_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class AllEventsController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<EventRepository>();

  late bool? completed;

  final RxBool _loading = false.obs;
  final RxList<Event> events = <Event>[].obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    completed = Get.arguments?['completed'];
    getEvents();
  }

  getEvents() async {
    _loading.value = true;

    // Get user from appService first, fallback to LocalStorage if needed
    User? user = appService.user;

    // If appService user is not properly initialized, try to get from LocalStorage
    if (user.families.isEmpty) {
      final localUser = LocalStorage.getUser();
      if (localUser != null && localUser.families.isNotEmpty) {
        user = localUser;
        // Update appService user if it wasn't properly set
        appService.user = localUser;
      } else {
        Utils.log('User families list is empty, cannot fetch events');
        _loading.value = false;
        Utils.showToast('Unable to load events. Please try again.');
        return;
      }
    }

    final result = await repository.getEvents(user.families.first.id!);
    result.fold((l) => Utils.showToast(l.message), (r) {
      events.value = r;
      events.refresh();
    });
    _loading.value = false;
  }
}
