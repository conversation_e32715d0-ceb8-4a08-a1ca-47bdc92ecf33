import 'package:family_management/app_service.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/domain/repositories/event_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class AllEventsController extends GetxController {
  final appService = Get.find<AppService>();
  final repository = gt<EventRepository>();

  late bool? completed;

  final RxBool _loading = false.obs;
  final RxList<Event> events = <Event>[].obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    completed = Get.arguments?['completed'];
    getEvents();
  }

  getEvents() async {
    _loading.value = true;
    final user = appService.user;
    final result = await repository.getEvents(user.families.first.id!);
    result.fold((l) => Utils.showToast(l.message), (r) {
      events.value = r;
      events.refresh();
    });
    _loading.value = false;
  }
}
