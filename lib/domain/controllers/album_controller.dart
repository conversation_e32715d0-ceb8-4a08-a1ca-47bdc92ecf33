import 'dart:developer';
import 'dart:io';

import 'package:family_management/app_service.dart';
import 'package:family_management/config/app_routes.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/controllers/gallery_controller.dart';
import 'package:family_management/domain/mixins/image_picker.dart';
import 'package:family_management/domain/models/album.dart';
import 'package:family_management/domain/models/media.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/album_repository.dart';
import 'package:family_management/domain/repositories/media_repository.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:family_management/utils/validation.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class AlbumController extends GetxController with ImagePickerMixin {
  final appService = Get.find<AppService>();
  final nameController = TextEditingController();
  final albumRepository = gt<AlbumRepository>();
  final mediaRepository = gt<MediaRepository>();
  final taskRepository = gt<TaskRepository>();
  final multiSelectController = MultiSelectController<String>();

  final RxList<Album> albums = <Album>[].obs;
  final RxList<Media> media = <Media>[].obs;
  final RxList<User> members = <User>[].obs;
  final RxList<User> selectedAssignees = <User>[].obs;
  final RxInt _selectedAlbum = 0.obs;
  final RxBool _loadingMedia = false.obs;
  final RxBool _loading = false.obs;
  final RxBool _loadingMembers = false.obs;
  final RxBool _creating = false.obs;
  final RxBool _uploading = false.obs;
  final RxString _nameError = ''.obs;
  final RxString _assigneesError = ''.obs;
  final RxDouble _progress = 0.0.obs;
  final Rx<File> _image = File('').obs;
  final RxBool _enableDeleteMedia = false.obs;
  final RxList<int> deleteIds = <int>[].obs;
  final RxBool _deletingMedia = false.obs;

  bool get loadingMedia => _loadingMedia.value;
  bool get loading => _loading.value;
  bool get loadingMembers => _loadingMembers.value;
  bool get creating => _creating.value;
  bool get uploading => _uploading.value;
  String get nameError => _nameError.value;
  String get assigneesError => _assigneesError.value;
  int get selectedAlbum => _selectedAlbum.value;
  double get progress => _progress.value;
  File get image => _image.value;
  bool get enableDeleteMedia => _enableDeleteMedia.value;
  bool get deletingMedia => _deletingMedia.value;

  set enableDeleteMedia(bool value) => _enableDeleteMedia.value = value;

  @override
  void onInit() {
    super.onInit();
    getAlbumns();
    _fetchMembers();
  }

  /// Fetch family members for assignee selection
  _fetchMembers() async {
    _loadingMembers.value = true;
    final result = await taskRepository.getMembers();
    result.fold((l) => Utils.showToast(l.message), (r) {
      members.value = r;
      members.refresh();
    });
    _loadingMembers.value = false;
  }

  initMedia(int albumId) async {
    _selectedAlbum.value = albumId;
    _loadingMedia.value = true;
    Get.toNamed(AppRoutes.albumGallery);
    await getMedia();
    _loadingMedia.value = false;
  }

  create() async {
    // Clear previous errors
    _nameError.value = '';
    _assigneesError.value = '';

    // Validate name
    _nameError.value = nameController.text.validateRequired() ?? '';

    // Get selected assignees
    final assigneeIds = multiSelectController.selectedItems
        .map((item) => int.parse(item.value))
        .toList();

    // Validate assignees (required)
    if (assigneeIds.isEmpty) {
      _assigneesError.value = 'Please select at least one family member'.tr;
    }

    // Check if there are any validation errors
    if (_nameError.value.isNotEmpty || _assigneesError.value.isNotEmpty) {
      return;
    }

    _creating.value = true;
    final result = await albumRepository.create(
        familyId: appService.user.families.first.id!,
        name: nameController.text,
        type: 1,
        assignedUserIds: assigneeIds);
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        Utils.showToast("Album created successfully".tr);
        getAlbumns();
        if (Get.isDialogOpen!) {
          Get.back();
        }
      },
    );
    nameController.clear();
    multiSelectController.clearAll();
    _creating.value = false;
  }

  getAlbumns() async {
    _loading.value = true;
    final result = await albumRepository.getAlbumns(type: 1);
    result.fold(
      (l) => Utils.showToast(l.message),
      (r) {
        albums.value = r;
        albums.refresh();
      },
    );
    _loading.value = false;
  }

  getMedia() async {
    log('getting media for album $selectedAlbum');
    final result = await albumRepository.getAlbum(selectedAlbum);
    result.fold((l) => Utils.showToast(l.message), (r) {
      media.value = r.media;
      media.refresh();
      log(r.media.length.toString());
    });
  }

  pickImageFromMedia() async {
    final image = await pickFromGallery();
    if (image == null) return;
    _image.value = image;
    uploadMedia();
  }

  pickImageFromCamera() async {
    final image = await pickFromCamera();
    if (image == null) return;
    _image.value = image;
    uploadMedia();
  }

  uploadMedia() async {
    media.add(Media(filePath: image.path));
    media.refresh();
    if (Get.isDialogOpen!) {
      Get.back();
    }
    _uploading.value = true;
    final result = await mediaRepository.create(
        image: image,
        albumId: selectedAlbum,
        onSendProgress: (sent, total) => _progress.value = sent / total);
    result.fold((l) => Utils.showToast(l.message), (r) {
      Utils.showToast("Media uploaded successfully".tr);
      getMedia();
    });
    _uploading.value = false;
  }

  select(int id) {
    if (deleteIds.contains(id)) {
      deleteIds.removeWhere((i) => i == id);
    } else {
      deleteIds.add(id);
    }
    deleteIds.refresh();
    update();
  }

  cancelDelete() {
    _enableDeleteMedia.value = false;
    deleteIds.clear();
    update();
  }

  deleteMedia() async {
    _deletingMedia.value = true;
    final result = await albumRepository.deleteMedia(deleteIds);
    result.fold((l) => Utils.showToast(l.message), (r) {
      Utils.showToast("Media deleted successfully".tr);
      cancelDelete();
      try {
        final mediaController = Get.find<GalleryController>();
        mediaController.getMedia();
      } catch (e) {
        log(e.toString());
      }
    });
    _deletingMedia.value = false;
  }
}
