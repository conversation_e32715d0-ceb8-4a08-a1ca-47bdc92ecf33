import 'package:family_management/app_service.dart';
import 'package:family_management/data/services/local_storage.dart';
import 'package:family_management/domain/models/task.dart';
import 'package:family_management/domain/models/user.dart';
import 'package:family_management/domain/repositories/task_repository.dart';
import 'package:family_management/injections.dart';
import 'package:family_management/utils/utils.dart';
import 'package:get/get.dart';

class AllTasksController extends GetxController {
  final appService = Get.find<AppService>();
  final respository = gt<TaskRepository>();

  late String? status;
  final RxBool _loading = false.obs;
  final RxList<Task> tasks = <Task>[].obs;

  bool get loading => _loading.value;

  @override
  void onInit() {
    super.onInit();
    status = Get.arguments?['status'];
    fetchTasks();
  }

  fetchTasks() async {
    _loading.value = true;

    // Get user from appService first, fallback to LocalStorage if needed
    User? user = appService.user;

    // If appService user is not properly initialized, try to get from LocalStorage
    if (user.families.isEmpty) {
      final localUser = LocalStorage.getUser();
      if (localUser != null && localUser.families.isNotEmpty) {
        user = localUser;
        // Update appService user if it wasn't properly set
        appService.user = localUser;
      } else {
        Utils.log('User families list is empty, cannot fetch tasks');
        _loading.value = false;
        Utils.showToast('Unable to load tasks. Please try again.');
        return;
      }
    }

    final result = await respository.getTasks(
        familyId: user.families.first.id!, status: status);
    result.fold((l) => Utils.showToast(l.message), (r) {
      tasks.value = r;
      tasks.refresh();
    });
    _loading.value = false;
  }
}
