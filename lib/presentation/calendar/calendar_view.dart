import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/calendar_controller.dart';
import 'package:family_management/domain/models/event.dart';
import 'package:family_management/presentation/widget/button.dart';
import 'package:family_management/presentation/widget/event_card.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' as intl;
import 'package:table_calendar/table_calendar.dart';

class CalendarView extends GetView<CalendarController> {
  const CalendarView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          extendBody: true,
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(bottom: 70),
            child: FloatingActionButton(
              heroTag: "calendar_fab",
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50)),
              onPressed: () => Get.toNamed(AppRoutes.addEvent),
              backgroundColor: primarySwatch,
              elevation: 4,
              child: const Icon(
                Icons.add,
                color: Colors.white,
              ),
            ),
          ),
          body: Padding(
            padding: const EdgeInsets.only(bottom: 90),
            child: NotificationListener<ScrollNotification>(
              onNotification: (scrollNotification) {
                controller.updateFade(scrollNotification.metrics.pixels);
                return true;
              },
              child: Container(
                height: MediaQuery.of(context).size.height,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: CustomScrollView(
                  shrinkWrap: true,
                  scrollDirection: Axis.vertical,
                  slivers: [
                    _buildCalendarHeader(context),
                    _buildCalendarWidget(context),
                    _buildSelectedDateHeader(context),
                    _buildEventsList(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarHeader(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.white,
      pinned: true,
      stretch: true,
      floating: false,
      expandedHeight: 80,
      flexibleSpace: FlexibleSpaceBar(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildNavigationButton(
              onTap: () => controller.backward(),
              icon: const Icon(Icons.chevron_left, size: 20),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextWidget(
                  text: intl.DateFormat.MMMM().format(controller.selectedDay),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: primarySwatch,
                ),
                TextWidget(
                  text: intl.DateFormat.y().format(controller.selectedDay),
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ],
            ),
            _buildNavigationButton(
              onTap: () => controller.forwared(),
              icon: const Icon(Icons.chevron_right, size: 20),
            ),
          ],
        ),
        centerTitle: true,
      ),
    );
  }

  Widget _buildCalendarWidget(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFF5F7FA),
              Color(0xFFE4ECF7),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: TableCalendar(
            eventLoader: controller.getEventsForDay,
            calendarStyle: CalendarStyle(
              isTodayHighlighted: true,
              outsideDaysVisible: false,
              // Normal days
              defaultTextStyle: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
              ),
              // Weekend days
              weekendTextStyle: TextStyle(
                color: Colors.grey[800],
                fontWeight: FontWeight.w500,
              ),
              // Today
              todayTextStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              todayDecoration: const BoxDecoration(
                color: pink,
                shape: BoxShape.circle,
              ),
              // Selected day
              selectedTextStyle: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              selectedDecoration: const BoxDecoration(
                color: primarySwatch,
                shape: BoxShape.circle,
              ),
              // Cell margins
              cellMargin: const EdgeInsets.all(6),
              // Cell padding
              cellPadding: const EdgeInsets.all(0),
            ),
            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: const TextStyle(
                color: primarySwatch,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              weekendStyle: const TextStyle(
                color: primarySwatch,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(150),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
            ),
            headerVisible: false,
            onDaySelected: (selectedDay, focusedDay) =>
                controller.selectDay(selectedDay),
            focusedDay: controller.selectedDay,
            firstDay: DateTime.utc(2010, 10, 16),
            lastDay: DateTime.utc(2030, 3, 14),
            selectedDayPredicate: (day) =>
                isSameDay(controller.selectedDay, day),
            calendarBuilders: CalendarBuilders(
              markerBuilder: (context, date, events) {
                if (events.isNotEmpty) {
                  return Positioned(
                    bottom: 2,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: pink.withAlpha(200),
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedDateHeader(BuildContext context) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.only(top: 16, bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: primarySwatch,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                TextWidget(
                  text: intl.DateFormat('EEEE, d MMMM y')
                      .format(controller.selectedDay),
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Divider(color: Colors.grey[300], height: 1),
          ],
        ),
      ),
    );
  }

  Widget _buildEventsList(BuildContext context) {
    if (controller.loading) {
      return const SliverFillRemaining(
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (controller.events.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.calendar_badge_minus,
                size: 80,
                color: Colors.grey[300],
              ),
              const SizedBox(height: 16),
              TextWidget(
                text: 'No events for this day'.tr,
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
              const SizedBox(height: 8),
              TextWidget(
                text: 'Tap the + button to add a new event'.tr,
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final item = controller.events[index];
          return _buildEventItem(context, item);
        },
        childCount: controller.events.length,
      ),
    );
  }

  Widget _buildEventItem(BuildContext context, Event item) {
    return Dismissible(
      key: Key(item.id.toString()),
      background: _buildDeleteBackground(),
      secondaryBackground: _buildDeleteBackground(),
      confirmDismiss: (direction) => _showDeleteConfirmation(context, item),
      onDismissed: (direction) => controller.deleteEvent(item.id!),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: EventCard(
          onPressed: (_) =>
              Get.toNamed(AppRoutes.eventDetails, arguments: item),
          event: item,
          width: MediaQuery.of(context).size.width - 40,
        ),
      ),
    );
  }

  Widget _buildDeleteBackground() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.red[400],
        borderRadius: BorderRadius.circular(15),
      ),
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.only(right: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.trash,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(height: 4),
          TextWidget(
            text: 'Delete'.tr,
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ],
      ),
    );
  }

  Future<bool?> _showDeleteConfirmation(BuildContext context, Event item) {
    return showCupertinoDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: TextWidget(
          text: 'Delete Event'.tr,
          fontWeight: FontWeight.bold,
          fontSize: 18,
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/svgs/confirm_delete.svg',
              height: 80,
            ),
            const SizedBox(height: 16),
            TextWidget(
              text: 'Are you sure you want to delete this event?'.tr,
              textAlign: TextAlign.center,
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ],
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Button(
                onPressed: () => Get.back(result: false),
                text: 'Cancel'.tr,
                textColor: Colors.grey[800],
                width: 120,
                backgroundColor: Colors.grey[200],
                radius: BorderRadius.circular(30),
              ),
              Button(
                onPressed: () {
                  controller.deleteEvent(item.id!);
                  Get.back(result: true);
                },
                text: 'Delete'.tr,
                textColor: Colors.white,
                width: 120,
                backgroundColor: Colors.red[400],
                radius: BorderRadius.circular(30),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButton({
    required Function() onTap,
    required Widget icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 36,
        width: 36,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: icon,
      ),
    );
  }
}
