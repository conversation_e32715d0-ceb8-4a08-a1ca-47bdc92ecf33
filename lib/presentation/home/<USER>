import 'package:family_management/domain/controllers/home_controller.dart';
import 'package:family_management/presentation/achievement_board/achievement_board_view.dart';
import 'package:family_management/presentation/calendar/calendar_view.dart';
import 'package:family_management/presentation/chat/home_chat_view.dart';
import 'package:family_management/presentation/profile/profile_view.dart';
import 'package:family_management/presentation/widget/bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../task/home_task_view.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        bottomNavigationBar: const BottomBar(),
        extendBody: true,
        body: IndexedStack(
          index: controller.activeIndex,
          children: const [
            AchievementBoardView(),
            HomeTaskView(),
            HomeChatView(),
            CalendarView(),
            ProfileView(),
          ],
        ),
      ),
    );
  }
}
