import 'package:family_management/config/app_routes.dart';
import 'package:family_management/domain/controllers/profile_controller.dart';
import 'package:family_management/presentation/widget/avatar_image.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import '../widget/custom_app_bar.dart';

class ProfileView extends GetView<ProfileController> {
  const ProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Directionality(
        textDirection: controller.appService.locale == const Locale('ar')
            ? TextDirection.rtl
            : TextDirection.ltr,
        child: Scaffold(
          floatingActionButton: FloatingActionButton(
            heroTag: "profile_fab",
            onPressed: () => Get.toNamed(AppRoutes.editProfile),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(50),
            ),
            backgroundColor: Colors.white,
            child: Icon(
              FontAwesomeIcons.penToSquare,
              color: Theme.of(context).primaryColor,
            ),
          ),
          body: Column(
            children: [
              Stack(
                clipBehavior: Clip.none,
                children: [
                  CustomAppBar(
                    title: 'The profile'.tr,
                    titleColor: Colors.white,
                  ),
                  Positioned(
                    right: 12,
                    top: 30,
                    child: PopupMenuButton(
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          onTap: () => controller.pickMembers(),
                          child: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/svgs/email.svg',
                                width: 20,
                                height: 20,
                              ),
                              const SizedBox(width: 10),
                              TextWidget(text: 'Invite members'.tr),
                            ],
                          ),
                        ),
                        // PopupMenuItem(
                        //   child: Row(
                        //     children: [
                        //       const Icon(Icons.language),
                        //       const SizedBox(width: 10),
                        //       TextWidget(text: 'Switch Language'.tr),
                        //     ],
                        //   ),
                        // ),
                      ],
                      icon: const Icon(
                        Icons.more_vert_rounded,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: -40,
                    left: 0,
                    right: 0,
                    child: Center(child: _buildProfileImage(context)),
                  ),
                ],
              ),
              const SizedBox(height: 60),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 30),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 20),
                      _buildInfoSection(),
                      const SizedBox(height: 20),
                      if (controller.members.isNotEmpty) ...[
                        TextWidget(
                          text: 'My family'.tr,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        _familyMembers()
                      ]
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _familyMembers() {
    return Obx(() => ListView.separated(
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          final item = controller.members[index];

          return Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.withAlpha(50),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Avatar without online status indicator
                AvatarImage(
                  url: item.avatar,
                  width: 50,
                  height: 50,
                ),

                const SizedBox(width: 12),

                // User info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextWidget(
                        text: item.name ?? '',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                      const SizedBox(height: 4),
                      TextWidget(
                        text: item.email ?? '',
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        itemCount: controller.members.length));
  }

  Widget _buildProfileImage(BuildContext context) {
    return GestureDetector(
      onTap: () => _showImagePicker(context),
      child: Center(
        child: SizedBox(
          width: 80,
          child: Obx(() => Stack(
                alignment: Alignment.bottomLeft,
                children: [
                  AvatarImage(
                      width: 80, height: 80, url: controller.user.avatar),
                  if (controller.uploading) ...[
                    // Semi-transparent overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black.withValues(alpha: 0.6),
                        ),
                      ),
                    ),

                    // Progress indicator
                    Positioned.fill(
                      child: CircularProgressIndicator(
                        value: controller.uploadProgress,
                        strokeWidth: 4,
                        backgroundColor: Colors.white.withValues(alpha: 0.3),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).primaryColor,
                        ),
                      ),
                    ),

                    // Percentage text
                    Positioned.fill(
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.cloud_upload,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${(controller.uploadProgress * 100).toInt()}%',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                  Positioned(
                      right: 0,
                      child: GestureDetector(
                        onTap: () => _showImagePicker(context),
                        child: Container(
                          width: 25,
                          height: 25,
                          decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Theme.of(context).primaryColor),
                          child: const Icon(
                            CupertinoIcons.camera,
                            size: 15,
                            color: Colors.white,
                          ),
                        ),
                      ))
                ],
              )),
        ),
      ),
    );
  }

  _showImagePicker(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        builder: (context) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        onTap: () => controller.pickImageFromGallery(),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 30),
                          child: Column(
                            children: [
                              SvgPicture.asset(
                                'assets/svgs/gallery.svg',
                                colorFilter: ColorFilter.mode(
                                    Theme.of(context).primaryColor,
                                    BlendMode.srcIn),
                                width: 40,
                                height: 40,
                              ),
                              const SizedBox(height: 10),
                              TextWidget(text: 'Gallery'.tr)
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 70),
                      GestureDetector(
                        onTap: () => controller.pickImageFromCamera(),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 30),
                          child: Column(
                            children: [
                              SvgPicture.asset(
                                'assets/svgs/camera.svg',
                                colorFilter: ColorFilter.mode(
                                    Theme.of(context).primaryColor,
                                    BlendMode.srcIn),
                                width: 40,
                                height: 40,
                              ),
                              const SizedBox(height: 10),
                              TextWidget(text: 'Camera'.tr)
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ));
  }

  Widget _buildInfoSection() {
    return GetBuilder<ProfileController>(
      builder: (controller) => Column(
        children: [
          _getRow(
              'assets/svgs/user.svg', 'Full name :'.tr, controller.user.name),
          const SizedBox(height: 15),
          _getRow('assets/svgs/email.svg', 'Your email :'.tr,
              controller.user.email),
          const SizedBox(height: 15),
          const SizedBox(height: 15),
          _getRow('assets/svgs/bio.svg', 'Your Bio :'.tr, controller.user.bio),
        ],
      ),
    );
  }

  Widget _getRow(String iconPath, String title, String? subtitle) {
    return Row(
      children: [
        SvgPicture.asset(
          iconPath,
          width: 20,
          height: 20,
        ),
        const SizedBox(width: 10),
        TextWidget(
          text: title,
          fontSize: 14,
          color: const Color(0xff808080),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: TextWidget(
            text: subtitle ?? '',
            fontSize: 12,
            color: const Color(0xFF134982),
          ),
        ),
      ],
    );
  }
}
