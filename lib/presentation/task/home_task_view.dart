import 'package:family_management/config/app_routes.dart';
import 'package:family_management/config/themes.dart';
import 'package:family_management/domain/controllers/task_controller.dart';
import 'package:family_management/presentation/task/task_view.dart';
import 'package:family_management/presentation/widget/text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HomeTaskView extends GetView<TaskController> {
  const HomeTaskView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TaskController>(
      builder: (controller) => Directionality(
        textDirection: controller.appService.direction,
        child: Scaffold(
          appBar: AppBar(
            title: TextWidget(
              text: 'Tasks'.tr,
              fontWeight: FontWeight.bold,
            ),
            centerTitle: true,
            elevation: 0,
            bottom: TabBar(
              controller: controller.tabController,
              indicatorColor: primarySwatch,
              indicatorWeight: 3,
              labelColor: primarySwatch,
              unselectedLabelColor: Colors.grey,
              tabs: [
                Tab(
                  icon: const Icon(Icons.people_outline),
                  text: 'All Tasks'.tr,
                ),
                Tab(
                  icon: const Icon(Icons.person_outline),
                  text: 'My Tasks'.tr,
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: controller.tabController,
            children: const [
              TaskView(mine: false), // All Tasks (index 0)
              TaskView(mine: true), // My Tasks (index 1)
            ],
          ),
          floatingActionButton: Padding(
            padding: const EdgeInsets.only(bottom: 70),
            child: FloatingActionButton(
              heroTag: "task_fab",
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(50)),
              backgroundColor: primarySwatch,
              elevation: 4,
              onPressed: () => Get.toNamed(AppRoutes.addTask),
              child: const Icon(
                Icons.add,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
